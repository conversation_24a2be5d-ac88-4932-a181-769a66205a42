package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.constant.EmailConstants;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

import lombok.extern.slf4j.Slf4j;

/**
 * Date utils.
 *
 * <AUTHOR> <PERSON>gi
 */
@Slf4j
public class DateUtils {

  private static final int HALF_HOUR = 30;
  private static final int END_OF_DAY_HOUR = 23;
  private static final int END_OF_DAY_MINUTE = 59;
  private static final int END_OF_DAY_SECOND = 59;
  private static final int END_OF_DAY_NANO = 999_999_999;

  private DateUtils() {}

  /**
   * Converts date to UTC string in format yyyy-MM-dd'T'HH:mm:ss'Z', also factors in time difference
   * of time zones.
   *
   * @param date the date
   * @param zone the zone
   * @return the string
   */
  public static String convertToUTCString(String date, String zone) {
    log.debug("Inside @method convertToUTCString. @param : date -> {} zone -> {}", date, zone);
    DateTimeFormatter inputFormatter =
        DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS);
    DateTimeFormatter outputFormatter =
        DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);

    LocalDateTime localDateTime = LocalDateTime.parse(date, inputFormatter);

    ZoneId zoneId = ZoneId.of(zone);
    ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
    ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
    return utcDateTime.format(outputFormatter);
  }

  /**
   * Only takes time and zone and in output provides date with time taking time difference of time
   * zones in consideration.
   *
   * @param time the time
   * @param zone the zone
   * @param daysToAdd the days to add
   * @return the string
   */
  public static String convertToUTCString(LocalTime time, String zone, int daysToAdd) {
    if (zone == null) zone = "UTC";
    LocalDate currentDate = LocalDate.now().plusDays(daysToAdd);
    LocalDateTime localDateTime = LocalDateTime.of(currentDate, time);
    ZoneId zoneId = ZoneId.of(zone);
    ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
    ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
    DateTimeFormatter outputFormatter =
        DateTimeFormatter.ofPattern(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_Z);
    return utcDateTime.format(outputFormatter);
  }

  public static LocalDateTime roundOffTime(LocalDateTime localDateTime) {
    int minute = localDateTime.getMinute();

    if (minute > 0 && minute <= HALF_HOUR) {
      localDateTime = localDateTime.withMinute(HALF_HOUR).withSecond(0);
    } else {
      localDateTime = localDateTime.plusHours(1).withMinute(0).withSecond(0);
    }

    return localDateTime;
  }

  /**
   * New This method is used to provide UTC date string (for current time or end of the day) for a
   * specific time zone.
   *
   * @param endOfDay the end of day (true for end of the day and false for now)
   * @param timeZone the time zone (time zone for which we want to get the UTC string)
   * @return the utc date string in yyyy-MM-dd'T'HH:mm:ss'Z' format
   */
  public static String getUTCDateTime(boolean endOfDay, String timeZone) {
    // Create ZoneId from the provided time zone string
    ZoneId zoneId = ZoneId.of(timeZone);

    ZonedDateTime zonedDateTime;
    if (endOfDay) {
      // Get the end of the day for the current date in the specified time zone
      LocalDateTime localEndOfDay = LocalDate.now(zoneId).atTime(END_OF_DAY_HOUR, END_OF_DAY_MINUTE, END_OF_DAY_SECOND, END_OF_DAY_NANO);
      zonedDateTime = localEndOfDay.atZone(zoneId);
    } else {
      // Get the current time in the specified time zone
      zonedDateTime = ZonedDateTime.now(zoneId);
    }

    // Convert the ZonedDateTime to UTC
    ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));

    // Format the ZonedDateTime to the required format
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
    return utcDateTime.format(formatter);
  }

  public static LocalDateTime convertToLocalDateTime(String dateString) {
    // Parse the string into a ZonedDateTime with UTC (Z)
    ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateString, DateTimeFormatter.ISO_DATE_TIME);

    // Convert ZonedDateTime to LocalDateTime (in the system default time zone)
    return zonedDateTime.withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime();
  }

  /**
   * Converts a LocalDateTime to a UTC ISO 8601 string with 'Z' at the end. Example output:
   * "2025-05-26T14:30:00Z"
   *
   * @param localDateTime LocalDateTime object
   * @return ISO 8601 formatted string in UTC
   */
  public static String convertToUtcIsoString(LocalDateTime localDateTime) {
    ZonedDateTime utcZoned = localDateTime.atZone(ZoneOffset.UTC);
    return utcZoned.format(DateTimeFormatter.ISO_INSTANT);
  }
  /**
   * Parses a date string in the format "yyyy-MM-dd'T'HH:mm:ss" to a Date object without timezone
   * consideration.
   *
   * @param dateString the date string to parse
   * @return the parsed Date object, or null if parsing fails
   */
  public static Date parseDateWithoutTZ(String dateString) {
    SimpleDateFormat formatter = new SimpleDateFormat(EmailConstants.YYYY_MM_DD_T_HH_MM_SS);
    //  formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
    Date date = null;
    try {
      date = formatter.parse(dateString);
    } catch (ParseException e) {
      log.error("Error inside @method parseDate.", e);
    }
    return date;
  }

  public static Date getFormattedDateTime(boolean endOfDay, String timeZone) {
    // Create ZoneId from the provided time zone string
    ZoneId zoneId = ZoneId.of(timeZone);

    ZonedDateTime zonedDateTime;
    if (endOfDay) {
      // Get the end of the day for the current date in the specified time zone
      LocalDateTime localEndOfDay = LocalDate.now(zoneId).atTime(LocalTime.MAX);
      zonedDateTime = localEndOfDay.atZone(zoneId);
    } else {
      // Get the current time in the specified time zone
      zonedDateTime = ZonedDateTime.now(zoneId);
    }

    // Convert the ZonedDateTime to UTC
    ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);

    // Return the Date object
    return Date.from(utcDateTime.toInstant());
  }

  public static Date parseDate(String dateString) {
    SimpleDateFormat formatter = new SimpleDateFormat(EmailConstants.YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
    formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
    Date date = null;
    try {
      date = formatter.parse(dateString);
    } catch (ParseException e) {
      log.error("Error inside @method parseDate.", e);
    }
    return date;
  }

  public static Date parseDateWithTimeZone(String dateStr, String timeZone) {
    // Parse the input date string (assuming it's in ISO 8601 format: yyyy-MM-dd'T'HH:mm:ss)
    LocalDateTime localDateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);

    // Apply the provided time zone to the local date-time
    ZoneId zoneId = ZoneId.of(timeZone);
    ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);

    // Convert the ZonedDateTime to UTC
    ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);

    // Return as a Date object
    return Date.from(utcDateTime.toInstant());
  }


}
