package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.dto.AttendeeAndStatus;
import com.enttribe.emailagent.dto.EventDto;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.PropertySet;
import microsoft.exchange.webservices.data.core.enumeration.property.MeetingResponseType;
import microsoft.exchange.webservices.data.core.enumeration.service.calendar.AppointmentType;
import microsoft.exchange.webservices.data.core.service.item.Appointment;
import microsoft.exchange.webservices.data.core.service.schema.AppointmentSchema;
import microsoft.exchange.webservices.data.property.complex.AttendeeCollection;
import microsoft.exchange.webservices.data.property.complex.EmailAddress;
import microsoft.exchange.webservices.data.property.complex.recurrence.pattern.Recurrence;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ConverterUtils {


    public static EventDto convertToEventDto(Appointment appointment) throws Exception {
        EventDto eventDto = new EventDto();

        eventDto.setId(appointment.getId().toString());
        eventDto.setOrganizer(appointment.getOrganizer().getAddress());
        AttendeeCollection requiredAttendees = appointment.getRequiredAttendees();
        AttendeeCollection optionalAttendees = appointment.getOptionalAttendees();
        List<String> list = requiredAttendees.getItems().stream().map(EmailAddress::getAddress).toList();
        List<AttendeeAndStatus> required = requiredAttendees.getItems().stream().map(attendee -> new AttendeeAndStatus(attendee.getAddress(),
                 attendee.getResponseType() != null
                ? attendee.getResponseType().toString()
                : MeetingResponseType.NoResponseReceived.toString())).toList();
        List<AttendeeAndStatus> optional = optionalAttendees.getItems().stream().map(attendee -> new AttendeeAndStatus(attendee.getAddress(),
                 attendee.getResponseType() != null
                ? attendee.getResponseType().toString()
                : MeetingResponseType.NoResponseReceived.toString())).toList();
        eventDto.setAttendees(list);
        eventDto.setSubject(appointment.getSubject());
        String bodyPreview;
        try {
            bodyPreview = appointment.getBody().toString();
        } catch (Exception e) {
            bodyPreview = null;
        }
        eventDto.setBodyPreview(bodyPreview);
        eventDto.setJoinUrl(appointment.getMeetingWorkspaceUrl());
        eventDto.setHasAttachments(appointment.getHasAttachments());
        eventDto.setMeetingStartTime(appointment.getStart());
        eventDto.setMeetingEndTime(appointment.getEnd());
        eventDto.setCreatedDateTime(appointment.getDateTimeCreated());
        eventDto.setLastModifiedDateTime(appointment.getLastModifiedTime());
        eventDto.setRequiredAttendees(required);
        eventDto.setOptionalAttendees(optional);
        eventDto.setLocation(appointment.getLocation());
        eventDto.setAccepted(appointment.getMyResponseType().toString());
        eventDto.setIsCancelled(appointment.getIsCancelled());

        // Populate recurring event fields
        populateRecurringEventFields(appointment, eventDto);

        return eventDto;
    }

    /**
     * Populates recurring event fields in EventDto from EWS Appointment
     */
    private static void populateRecurringEventFields(Appointment appointment, EventDto eventDto) {
        try {
            // Load additional properties needed for recurring events
            PropertySet propertySet = new PropertySet(
                AppointmentSchema.AppointmentType,
                AppointmentSchema.Recurrence,
//                AppointmentSchema.Uid,
                AppointmentSchema.AppointmentSequenceNumber

            );
            appointment.load(propertySet);

            // Get appointment type (Single, Occurrence, Exception, SeriesMaster)
            AppointmentType appointmentType = appointment.getAppointmentType();
            eventDto.setType(appointmentType.toString());

            // Handle different appointment types
            switch (appointmentType) {
                case Single:
                    // Non-recurring appointment
                    eventDto.setSeriesMasterId(null);
                    eventDto.setOccurrenceId(null);
                    eventDto.setRecurrence(null);
                    break;

                case RecurringMaster:
                    // This is the series master (recurring series definition)
                    eventDto.setSeriesMasterId(appointment.getId().toString());
                    eventDto.setOccurrenceId(null);

                    // Get recurrence pattern
                    Recurrence recurrence = appointment.getRecurrence();
                    if (recurrence != null) {
                        eventDto.setRecurrence(convertRecurrenceToJson(recurrence));
                    }
                    break;

                case Occurrence:
                case Exception:
                    // This is an occurrence or exception of a recurring series
                    // Get the series master ID
                    try {
                        // For occurrences, we need to get the series master
                        Appointment seriesMaster = appointment.bindToRecurringMaster(appointment.getService(), appointment.getId());
                        if (seriesMaster != null) {
                            eventDto.setSeriesMasterId(seriesMaster.getId().toString());

                            // Get recurrence pattern from series master
                            seriesMaster.load(new PropertySet(AppointmentSchema.Recurrence));
                            Recurrence masterRecurrence = seriesMaster.getRecurrence();
                            if (masterRecurrence != null) {
                                eventDto.setRecurrence(convertRecurrenceToJson(masterRecurrence));
                            }
                        }
                    } catch (Exception e) {
                        log.warn("Could not bind to recurring master for appointment: {}", e.getMessage());
                        eventDto.setSeriesMasterId(null);
                    }

                    // Set occurrence ID (this is the ID of this specific occurrence)
                    eventDto.setOccurrenceId(appointment.getId().toString());
                    break;
            }

        } catch (Exception e) {
            log.error("Error populating recurring event fields: {}", e.getMessage(), e);
            // Set default values on error
            eventDto.setType("Single");
            eventDto.setSeriesMasterId(null);
            eventDto.setOccurrenceId(null);
            eventDto.setRecurrence(null);
        }
    }

    /**
     * Converts EWS Recurrence pattern to JSON string
     */
    private static String convertRecurrenceToJson(Recurrence recurrence) {
        try {
            Map<String, Object> recurrenceMap = new HashMap<>();
            Map<String, Object> pattern = new HashMap<>();
            Map<String, Object> range = new HashMap<>();

            // Get pattern information
            if (recurrence instanceof Recurrence.DailyPattern) {
                Recurrence.DailyPattern dailyPattern = (Recurrence.DailyPattern) recurrence;
                pattern.put("type", "daily");
                pattern.put("interval", dailyPattern.getInterval());
            } else if (recurrence instanceof Recurrence.WeeklyPattern) {
                Recurrence.WeeklyPattern weeklyPattern = (Recurrence.WeeklyPattern) recurrence;
                pattern.put("type", "weekly");
                pattern.put("interval", weeklyPattern.getInterval());
                pattern.put("daysOfWeek", weeklyPattern.getDaysOfTheWeek());
            } else if (recurrence instanceof Recurrence.MonthlyPattern) {
                Recurrence.MonthlyPattern monthlyPattern = (Recurrence.MonthlyPattern) recurrence;
                pattern.put("type", "absoluteMonthly");
                pattern.put("interval", monthlyPattern.getInterval());
                pattern.put("dayOfMonth", monthlyPattern.getDayOfMonth());
            } else if (recurrence instanceof Recurrence.YearlyPattern) {
                Recurrence.YearlyPattern yearlyPattern = (Recurrence.YearlyPattern) recurrence;
                pattern.put("type", "absoluteYearly");
                pattern.put("dayOfMonth", yearlyPattern.getDayOfMonth());
                pattern.put("month", yearlyPattern.getMonth().ordinal() + 1);
            }

            // Get range information
            if (recurrence.getStartDate() != null) {
                range.put("startDate", recurrence.getStartDate().toString());
            }

            if (recurrence.getEndDate() != null) {
                range.put("endDate", recurrence.getEndDate().toString());
                range.put("type", "endDate");
            } else if (recurrence.getNumberOfOccurrences() != null) {
                range.put("numberOfOccurrences", recurrence.getNumberOfOccurrences());
                range.put("type", "numbered");
            } else {
                range.put("type", "noEnd");
            }

            recurrenceMap.put("pattern", pattern);
            recurrenceMap.put("range", range);

            // Convert to JSON string
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(recurrenceMap);

        } catch (Exception e) {
            log.error("Error converting recurrence to JSON: {}", e.getMessage(), e);
            return null;
        }
    }
}
