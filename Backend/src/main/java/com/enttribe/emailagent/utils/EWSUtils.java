package com.enttribe.emailagent.utils;

import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.enumeration.misc.ConnectingIdType;
import microsoft.exchange.webservices.data.core.enumeration.misc.ExchangeVersion;
import microsoft.exchange.webservices.data.credential.WebCredentials;
import microsoft.exchange.webservices.data.misc.ImpersonatedUserId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;

@Slf4j
@Component
public class EWSUtils {


    private static String serviceAccountUsername;


    private static String serviceAccountPassword;


    private static String ewsURL;

    @Value("${ews.serviceAccountUsername}")
    public void setServiceAccountUsername(String serviceAccountUsername) {
        EWSUtils.serviceAccountUsername = serviceAccountUsername;
    }

    @Value("${ews.serviceAccountPassword}")
    public void setServiceAccountPassword(String serviceAccountPassword) {
        EWSUtils.serviceAccountPassword = serviceAccountPassword;
    }

    @Value("${ews.ewsURL}")
    public void setEwsURL(String ewsURL) {
        EWSUtils.ewsURL = ewsURL;
    }

    public static ExchangeService getServiceObjectForUser(String email) throws URISyntaxException {
        log.debug("EWS parameters for @method getServiceObjectForUser are {} {}", email, ewsURL);
        ExchangeService service = getServiceObject();
        ImpersonatedUserId impersonatedUserId = new ImpersonatedUserId(ConnectingIdType.SmtpAddress, email);
        service.setImpersonatedUserId(impersonatedUserId);
        return service;
    }

    public static ExchangeService getServiceObject() throws URISyntaxException {
        log.debug("EWS parameters are {} {}", serviceAccountUsername, ewsURL);
        ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP2);
        service.setCredentials(new WebCredentials(serviceAccountUsername, serviceAccountPassword));
        service.setUrl(new URI(ewsURL));
        return service;
    }

}
